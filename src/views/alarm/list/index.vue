<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <div class="filter-container mb-3">
      <Filter :query-params="queryParams" @query="handleQuery" @reset="resetQuery" />
    </div>

    <!-- 数据列表 -->
    <div class="data-list-container">
      <div class="card-header-container">
        <h5 class="card-title">告警明细列表</h5>
      </div>

      <!-- 表格 -->
      <div class="table-wrapper">
        <el-table
          :data="alarmDetailsList"
          v-loading="loading"
          style="width: 100%"
          :header-cell-style="{ backgroundColor: '#f2f3f5' }"
        >
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="alarmTypeName" label="隐患类型" min-width="120" />
          <el-table-column prop="confidence" label="置信度" width="100">
            <template #default="{ row }">
              <el-tag :type="getConfidenceColor(row.confidence)">
                {{ row.confidence }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="alarmTime" label="告警时间" width="120" />
          <el-table-column prop="taskId" label="任务ID" width="100" />
          <el-table-column prop="creator" label="创建人" width="100" />
          <el-table-column prop="createTime" label="创建时间" width="160">
            <template #default="{ row }">
              {{ formatDate(new Date(row.createTime)) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-button link type="primary" @click="handleView(row, alarmDetailDialogRef)"
                >查看</el-button
              >
              <el-button link type="danger" @click="handleDelete(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNo"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="PAGE_SIZES as number[]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="getList"
          @current-change="getList"
        />
      </div>
    </div>

    <!-- 告警详情弹窗 -->
    <AlarmDetailDialog ref="alarmDetailDialogRef" @success="handleDetailSuccess" />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import Filter from './components/Filter.vue'
import AlarmDetailDialog from './components/AlarmDetailDialog.vue'
import { useList } from './useList'
import { PAGE_SIZES } from './constants'
import { formatDate } from '@/utils/formatTime'

defineOptions({ name: 'AlarmDetails' })

const alarmDetailDialogRef = ref()

const {
  loading,
  total,
  alarmDetailsList,
  queryParams,
  getList,
  handleQuery,
  handleView,
  handleDelete
} = useList()

/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.taskId = undefined
  queryParams.alarmType = undefined
  queryParams.alarmTypeName = undefined
  queryParams.createTime = undefined
  handleQuery()
}

/** 获取置信度颜色 */
const getConfidenceColor = (confidence: string) => {
  const confidenceNum = parseFloat(confidence)
  if (confidenceNum >= 0.8) return 'danger'
  if (confidenceNum >= 0.6) return 'warning'
  return 'info'
}

/** 详情弹窗成功回调 */
const handleDetailSuccess = () => {
  getList()
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.app-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
}
.mb-4 {
  margin-bottom: 16px;
}
.card-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}
.data-list-container {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 4px;
  padding: 14px 20px;
  height: calc(100vh - 158px);
  box-sizing: border-box;
}
.table-wrapper {
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  margin-top: 14px;
}
.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding-top: 20px;
}
.filter-container {
  background-color: #fff;
  border-radius: 4px;
  box-sizing: border-box;
  padding: 20px;
  padding-bottom: 0;
}
.card-header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
