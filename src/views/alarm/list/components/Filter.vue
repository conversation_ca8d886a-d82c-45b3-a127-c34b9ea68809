<template>
  <el-form :model="localParams" ref="queryFormRef" :inline="true">
    <el-form-item label="任务ID" prop="taskId">
      <el-input
        v-model="localParams.taskId"
        placeholder="请输入任务ID"
        clearable
        class="!w-240px"
        @keyup.enter="handleQuery"
      />
    </el-form-item>
    <el-form-item label="隐患类型" prop="alarmType">
      <el-input
        v-model="localParams.alarmType"
        placeholder="请输入隐患类型"
        clearable
        class="!w-240px"
        @keyup.enter="handleQuery"
      />
    </el-form-item>
    <el-form-item label="隐患类型名称" prop="alarmTypeName">
      <el-input
        v-model="localParams.alarmTypeName"
        placeholder="请输入隐患类型名称"
        clearable
        class="!w-240px"
        @keyup.enter="handleQuery"
      />
    </el-form-item>
    <el-form-item label="创建时间" prop="createTime">
      <el-date-picker
        v-model="localParams.createTime"
        type="datetimerange"
        range-separator="至"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        class="!w-240px"
      />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="handleQuery">搜索</el-button>
      <el-button @click="resetQuery">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import type { QueryParams } from '../types'

const props = defineProps<{
  queryParams: QueryParams
}>()

const emit = defineEmits<{
  query: []
  reset: []
}>()

const queryFormRef = ref()
const localParams = reactive({ ...props.queryParams })

watch(
  () => props.queryParams,
  (newVal) => {
    Object.assign(localParams, newVal)
  },
  { deep: true }
)

const handleQuery = () => {
  Object.assign(props.queryParams, localParams)
  emit('query')
}

const resetQuery = () => {
  queryFormRef.value.resetFields()
  emit('reset')
}
</script>

