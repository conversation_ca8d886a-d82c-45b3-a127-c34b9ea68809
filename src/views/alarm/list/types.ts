// 告警明细相关类型定义
export interface AlarmDetails {
  id: number
  taskId: number
  taskFileId: number
  alarmType: string
  alarmTypeName: string
  confidence: string
  alarmFileUrl: string
  alarmFileId: string
  alarmTime: string
  createTime: string
  creator: string
  updateTime: string
  updater: string
  deleted: boolean
  tenantId: number
}

export interface QueryParams {
  pageNo: number
  pageSize: number
  taskId?: number
  alarmTypeName?: string
  createTime?: string[]
}
