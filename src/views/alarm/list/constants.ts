// 任务相关常量
export const TASK_TYPES = {
  DATASET: 0,
  VIDEO_STREAM: 1
} as const

export const TASK_TYPE_LABELS = {
  [TASK_TYPES.DATASET]: '数据集',
  [TASK_TYPES.VIDEO_STREAM]: '视频流'
} as const

// 任务状态枚举（根据实际业务调整）
export const TASK_STATUS = {
  PENDING: 0,
  RUNNING: 1,
  SUCCESS: 2,
  FAILED: 3,
  CANCELLED: 4
} as const

export const TASK_STATUS_LABELS = {
  [TASK_STATUS.PENDING]: '待执行',
  [TASK_STATUS.RUNNING]: '执行中',
  [TASK_STATUS.SUCCESS]: '执行成功',
  [TASK_STATUS.FAILED]: '执行失败',
  [TASK_STATUS.CANCELLED]: '已取消'
} as const

export const TASK_STATUS_COLORS = {
  [TASK_STATUS.PENDING]: 'info',
  [TASK_STATUS.RUNNING]: 'warning',
  [TASK_STATUS.SUCCESS]: 'success',
  [TASK_STATUS.FAILED]: 'danger',
  [TASK_STATUS.CANCELLED]: 'info'
} as const

export const PAGE_SIZES = [10, 20, 50, 100]
